## libyaEngineNext 测试程序(演示插件开发与基本用法)
演示了一个虚构的协议 story 的解析，验证 tcp 重组效果;

## 构建步骤

### 依赖
构建并安装 libyaEngineNext;

**ASN.1协议支持 (可选)**:
- 默认情况下，ASN.1协议解析器**不会被编译**
- 如需ASN.1支持，需要安装asn1c工具：
```shell
# Ubuntu/Debian
sudo apt-get install asn1c

# CentOS/RHEL
sudo yum install asn1c
```

### 快速构建 (默认，不包含ASN.1)
```shell
cmake3 -S . -B build && cmake3 --build build
```

### ASN.1编译配置

#### 手动CMake配置
```shell
# 启用ASN.1编译
cmake3 -S . -B build -DENABLE_ASN1_PROTOCOLS=ON
cmake3 --build build

# 禁用ASN.1编译 (默认)
cmake3 -S . -B build -DENABLE_ASN1_PROTOCOLS=OFF
cmake3 --build build
```

### 测试
```shell
echo "/usr/local/lib64" >>/etc/ld.so.conf && ldconfig
cmake3 --build build -t test
```
