#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "ipv6cp"

// IPv6CP Message Types (RFC 2023)
typedef enum {
    IPV6CP_CODE_CONFIGURE_REQUEST = 1,
    IPV6CP_CODE_CONFIGURE_ACK = 2,
    IPV6CP_CODE_CONFIGURE_NAK = 3,
    IPV6CP_CODE_CONFIGURE_REJECT = 4,
    IPV6CP_CODE_TERMINATE_REQUEST = 5,
    IPV6CP_CODE_TERMINATE_ACK = 6,
    IPV6CP_CODE_CODE_REJECT = 7
} ipv6cp_code_t;

// IPv6CP Configuration Options (RFC 2023)
typedef enum {
    IPV6CP_OPTION_INTERFACE_TOKEN = 1,
    IPV6CP_OPTION_IPV6_COMPRESSION = 2
} ipv6cp_option_t;

static const char* ipv6cp_code_name(uint8_t code)
{
    switch (code) {
        case IPV6CP_CODE_CONFIGURE_REQUEST:  return "Configure-Request";
        case IPV6CP_CODE_CONFIGURE_ACK:      return "Configure-Ack";
        case IPV6CP_CODE_CONFIGURE_NAK:      return "Configure-Nak";
        case IPV6CP_CODE_CONFIGURE_REJECT:   return "Configure-Reject";
        case IPV6CP_CODE_TERMINATE_REQUEST:  return "Terminate-Request";
        case IPV6CP_CODE_TERMINATE_ACK:      return "Terminate-Ack";
        case IPV6CP_CODE_CODE_REJECT:        return "Code-Reject";
        default:                             return "Unknown";
    }
}

static const char* ipv6cp_option_name(uint8_t option)
{
    switch (option) {
        case IPV6CP_OPTION_INTERFACE_TOKEN:   return "Interface-Token";
        case IPV6CP_OPTION_IPV6_COMPRESSION:  return "IPv6-Compression-Protocol";
        default:                              return "Unknown";
    }
}

static int parse_ipv6cp_options(nxt_mbuf_t *mbuf, int offset, int options_length, precord_t *precord)
{
    int parsed = 0;
    int option_count = 0;

    // Create options array
    ya_fvalue_t* fvArray = precord_put(precord, "options", array);

    while (parsed < options_length && option_count < 10) { // Limit to 10 options
        if (nxt_mbuf_get_length(mbuf) < offset + parsed + 2) {
            break;
        }

        uint8_t option_type = nxt_mbuf_get_uint8(mbuf, offset + parsed);
        uint8_t option_length = nxt_mbuf_get_uint8(mbuf, offset + parsed + 1);

        if (option_length < 2 || parsed + option_length > options_length) {
            break;
        }

        printf("IPv6CP: Option %d - Type=%s (%d), Length=%d\n",
               option_count, ipv6cp_option_name(option_type), option_type, option_length);

        // Add option to array as a table
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);
        precord_sub_put(precord, fvTable, "type", uinteger, YA_FT_UINT8, option_type);
        precord_sub_put(precord, fvTable, "type_name", string, YA_FT_STRING, ipv6cp_option_name(option_type));
        precord_sub_put(precord, fvTable, "length", uinteger, YA_FT_UINT8, option_length);

        // Parse option data based on type
        if (option_type == IPV6CP_OPTION_INTERFACE_TOKEN && option_length == 6) {
            uint32_t interface_token = nxt_mbuf_get_uint32_ntoh(mbuf, offset + parsed + 2);
            precord_sub_put(precord, fvTable, "interface_token", uinteger, YA_FT_UINT32, interface_token);
        } else if (option_type == IPV6CP_OPTION_IPV6_COMPRESSION && option_length >= 4) {
            uint16_t compression_protocol = nxt_mbuf_get_uint16_ntoh(mbuf, offset + parsed + 2);
            precord_sub_put(precord, fvTable, "compression_protocol", uinteger, YA_FT_UINT16, compression_protocol);
        }

        parsed += option_length;
        option_count++;
    }

    precord_put(precord, "num_options", uinteger, option_count);
    return parsed;
}

static
int ipv6cp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum IPv6CP header length (4 bytes)
    if (nxt_mbuf_get_length(mbuf) < 4) {
        printf("IPv6CP: insufficient data length (%d bytes, need at least 4)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse IPv6CP header
    uint8_t code = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t identifier = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    // Record IPv6CP fields
    precord_put(precord, "code", uinteger, code);
    precord_put(precord, "code_name", string, ipv6cp_code_name(code));
    precord_put(precord, "identifier", uinteger, identifier);
    precord_put(precord, "length", uinteger, length);

    printf("IPv6CP: Code=%s (%d), ID=%d, Length=%d\n", 
           ipv6cp_code_name(code), code, identifier, length);

    int consumed = 4;

    // Parse options for Configure messages
    if ((code == IPV6CP_CODE_CONFIGURE_REQUEST || 
         code == IPV6CP_CODE_CONFIGURE_ACK || 
         code == IPV6CP_CODE_CONFIGURE_NAK || 
         code == IPV6CP_CODE_CONFIGURE_REJECT) && length > 4) {
        
        int options_length = length - 4;
        if (nxt_mbuf_get_length(mbuf) >= length) {
            parse_ipv6cp_options(mbuf, 4, options_length, precord);
        }
        consumed = length;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);
    return consumed;
}

static
int ipv6cp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "ipv6 control protocol");
    
    // IPv6CP header fields
    pschema_register_field(pschema, "code", YA_FT_UINT8, "message code");
    pschema_register_field(pschema, "code_name", YA_FT_STRING, "message code name");
    pschema_register_field(pschema, "identifier", YA_FT_UINT8, "message identifier");
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    
    // Options
    pschema_register_field(pschema, "num_options", YA_FT_UINT8, "number of options");
    pschema_register_field(pschema, "options", YA_FT_ARRAY, "IPv6CP options array");
    
    return 0;
}

// PPP Protocol Numbers
typedef enum {
    PPP_PROTOCOL_IPV6CP = 0x8057  // IPv6 Control Protocol
} ppp_protocol_t;

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "ipv6cp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = ipv6cp_schema_reg,
    .dissectFun   = ipv6cp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // IPv6CP is mounted via PPP handoff, not directly
        NXT_MNT_NUMBER("ppp", PPP_PROTOCOL_IPV6CP),

        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(ipv6cp)
{
    nxt_dissector_register(&gDissectorDef);
}
