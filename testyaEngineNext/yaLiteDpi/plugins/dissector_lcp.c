#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "lcp"

// LCP Message Types (RFC 1661)
typedef enum {
    LCP_CODE_CONFIGURE_REQUEST = 1,
    LCP_CODE_CONFIGURE_ACK = 2,
    LCP_CODE_CONFIGURE_NAK = 3,
    LCP_CODE_CONFIGURE_REJECT = 4,
    LCP_CODE_TERMINATE_REQUEST = 5,
    L<PERSON>_CODE_TERMINATE_ACK = 6,
    LCP_CODE_CODE_REJECT = 7,
    LCP_CODE_PROTOCOL_REJECT = 8,
    LCP_CODE_ECHO_REQUEST = 9,
    LCP_CODE_ECHO_REPLY = 10,
    L<PERSON>_CODE_DISCARD_REQUEST = 11
} lcp_code_t;

// LCP Configuration Options (RFC 1661)
typedef enum {
    LCP_OPTION_MRU = 1,
    LCP_OPTION_ACCM = 2,
    LCP_OPTION_AUTHENTICATION_PROTOCOL = 3,
    LCP_OPTION_QUALITY_PROTOCOL = 4,
    LCP_OPTION_MAGIC_NUMBER = 5,
    LCP_OPTION_PROTOCOL_COMPRESSION = 7,
    LCP_OPTION_ADDR_CTRL_COMPRESSION = 8,
    LCP_OPTION_CALLBACK = 13
} lcp_option_t;

static const char* lcp_code_name(uint8_t code)
{
    switch (code) {
        case LCP_CODE_CONFIGURE_REQUEST:  return "Configure-Request";
        case LCP_CODE_CONFIGURE_ACK:      return "Configure-Ack";
        case LCP_CODE_CONFIGURE_NAK:      return "Configure-Nak";
        case LCP_CODE_CONFIGURE_REJECT:   return "Configure-Reject";
        case LCP_CODE_TERMINATE_REQUEST:  return "Terminate-Request";
        case LCP_CODE_TERMINATE_ACK:      return "Terminate-Ack";
        case LCP_CODE_CODE_REJECT:        return "Code-Reject";
        case LCP_CODE_PROTOCOL_REJECT:    return "Protocol-Reject";
        case LCP_CODE_ECHO_REQUEST:       return "Echo-Request";
        case LCP_CODE_ECHO_REPLY:         return "Echo-Reply";
        case LCP_CODE_DISCARD_REQUEST:    return "Discard-Request";
        default:                          return "Unknown";
    }
}

static const char* lcp_option_name(uint8_t option)
{
    switch (option) {
        case LCP_OPTION_MRU:                      return "Maximum-Receive-Unit";
        case LCP_OPTION_ACCM:                     return "Async-Control-Character-Map";
        case LCP_OPTION_AUTHENTICATION_PROTOCOL:  return "Authentication-Protocol";
        case LCP_OPTION_QUALITY_PROTOCOL:         return "Quality-Protocol";
        case LCP_OPTION_MAGIC_NUMBER:             return "Magic-Number";
        case LCP_OPTION_PROTOCOL_COMPRESSION:     return "Protocol-Field-Compression";
        case LCP_OPTION_ADDR_CTRL_COMPRESSION:    return "Address-and-Control-Field-Compression";
        case LCP_OPTION_CALLBACK:                 return "Callback";
        default:                                  return "Unknown";
    }
}

static int parse_lcp_options(nxt_mbuf_t *mbuf, int offset, int options_length, precord_t *precord)
{
    int parsed = 0;
    int option_count = 0;

    // Create options array
    ya_fvalue_t* fvArray = precord_put(precord, "options", array);

    while (parsed < options_length && option_count < 10) { // Limit to 10 options
        if (nxt_mbuf_get_length(mbuf) < offset + parsed + 2) {
            break;
        }

        uint8_t option_type = nxt_mbuf_get_uint8(mbuf, offset + parsed);
        uint8_t option_length = nxt_mbuf_get_uint8(mbuf, offset + parsed + 1);

        if (option_length < 2 || parsed + option_length > options_length) {
            break;
        }

        printf("LCP: Option %d - Type=%s (%d), Length=%d\n",
               option_count, lcp_option_name(option_type), option_type, option_length);

        // Add option to array as a table
        ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);
        precord_sub_put(precord, fvTable, "type", uinteger, YA_FT_UINT8, option_type);
        precord_sub_put(precord, fvTable, "type_name", string, YA_FT_STRING, lcp_option_name(option_type));
        precord_sub_put(precord, fvTable, "length", uinteger, YA_FT_UINT8, option_length);

        // Parse option data based on type
        if (option_type == LCP_OPTION_MRU && option_length == 4) {
            uint16_t mru = nxt_mbuf_get_uint16_ntoh(mbuf, offset + parsed + 2);
            precord_sub_put(precord, fvTable, "mru", uinteger, YA_FT_UINT16, mru);
        } else if (option_type == LCP_OPTION_MAGIC_NUMBER && option_length == 6) {
            uint32_t magic_number = nxt_mbuf_get_uint32_ntoh(mbuf, offset + parsed + 2);
            precord_sub_put(precord, fvTable, "magic_number", uinteger, YA_FT_UINT32, magic_number);
        } else if (option_type == LCP_OPTION_AUTHENTICATION_PROTOCOL && option_length >= 4) {
            uint16_t auth_protocol = nxt_mbuf_get_uint16_ntoh(mbuf, offset + parsed + 2);
            precord_sub_put(precord, fvTable, "auth_protocol", uinteger, YA_FT_UINT16, auth_protocol);
        }

        parsed += option_length;
        option_count++;
    }

    precord_put(precord, "num_options", uinteger, option_count);
    return parsed;
}

static
int lcp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum LCP header length (4 bytes)
    if (nxt_mbuf_get_length(mbuf) < 4) {
        printf("LCP: insufficient data length (%d bytes, need at least 4)\n", 
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    // Parse LCP header
    uint8_t code = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t identifier = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    // Record LCP fields
    precord_put(precord, "code", uinteger, code);
    precord_put(precord, "code_name", string, lcp_code_name(code));
    precord_put(precord, "identifier", uinteger, identifier);
    precord_put(precord, "length", uinteger, length);

    printf("LCP: Code=%s (%d), ID=%d, Length=%d\n", 
           lcp_code_name(code), code, identifier, length);

    int consumed = 4;

    // Parse options for Configure messages
    if ((code == LCP_CODE_CONFIGURE_REQUEST || 
         code == LCP_CODE_CONFIGURE_ACK || 
         code == LCP_CODE_CONFIGURE_NAK || 
         code == LCP_CODE_CONFIGURE_REJECT) && length > 4) {
        
        int options_length = length - 4;
        if (nxt_mbuf_get_length(mbuf) >= length) {
            parse_lcp_options(mbuf, 4, options_length, precord);
        }
        consumed = length;
    }

    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);
    return consumed;
}

static
int lcp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "link control protocol");
    
    // LCP header fields
    pschema_register_field(pschema, "code", YA_FT_UINT8, "message code");
    pschema_register_field(pschema, "code_name", YA_FT_STRING, "message code name");
    pschema_register_field(pschema, "identifier", YA_FT_UINT8, "message identifier");
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    
    // Options
    pschema_register_field(pschema, "num_options", YA_FT_UINT8, "number of options");
    pschema_register_field(pschema, "options", YA_FT_ARRAY, "LCP options array");
    
    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "lcp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = lcp_schema_reg,
    .dissectFun   = lcp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // LCP is mounted via PPP handoff, not directly
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(lcp)
{
    nxt_dissector_register(&gDissectorDef);
}
