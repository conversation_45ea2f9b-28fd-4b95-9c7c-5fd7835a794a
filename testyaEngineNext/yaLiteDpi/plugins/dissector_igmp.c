#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "igmp"

// IGMP Message Types
typedef enum {
    IGMP_TYPE_MEMBERSHIP_QUERY = 0x11,
    IGMP_TYPE_V1_MEMBERSHIP_REPORT = 0x12,
    IGMP_TYPE_V2_MEMBERSHIP_REPORT = 0x16,
    IGMP_TYPE_V2_LEAVE_GROUP = 0x17,
    IGMP_TYPE_V3_MEMBERSHIP_REPORT = 0x22
} igmp_type_t;

// IGMPv3 Group Record Types
typedef enum {
    IGMPV3_MODE_IS_INCLUDE = 1,
    IGMPV3_MODE_IS_EXCLUDE = 2,
    IGMPV3_CHANGE_TO_INCLUDE = 3,
    IGMPV3_CHANGE_TO_EXCLUDE = 4,
    IGMPV3_ALLOW_NEW_SOURCES = 5,
    IGMPV3_BLOCK_OLD_SOURCES = 6
} igmpv3_record_type_t;

static const char* igmp_type_name(uint8_t type)
{
    switch (type) {
        case IGMP_TYPE_MEMBERSHIP_QUERY:     return "Membership Query";
        case IGMP_TYPE_V1_MEMBERSHIP_REPORT: return "IGMPv1 Membership Report";
        case IGMP_TYPE_V2_MEMBERSHIP_REPORT: return "IGMPv2 Membership Report";
        case IGMP_TYPE_V2_LEAVE_GROUP:       return "IGMPv2 Leave Group";
        case IGMP_TYPE_V3_MEMBERSHIP_REPORT: return "IGMPv3 Membership Report";
        default:                             return "Unknown";
    }
}

static const char* igmpv3_record_type_name(uint8_t type)
{
    switch (type) {
        case IGMPV3_MODE_IS_INCLUDE:     return "MODE_IS_INCLUDE";
        case IGMPV3_MODE_IS_EXCLUDE:     return "MODE_IS_EXCLUDE";
        case IGMPV3_CHANGE_TO_INCLUDE:   return "CHANGE_TO_INCLUDE";
        case IGMPV3_CHANGE_TO_EXCLUDE:   return "CHANGE_TO_EXCLUDE";
        case IGMPV3_ALLOW_NEW_SOURCES:   return "ALLOW_NEW_SOURCES";
        case IGMPV3_BLOCK_OLD_SOURCES:   return "BLOCK_OLD_SOURCES";
        default:                         return "Unknown";
    }
}

static int igmp_version_from_type(uint8_t type)
{
    switch (type) {
        case IGMP_TYPE_V1_MEMBERSHIP_REPORT:
            return 1;
        case IGMP_TYPE_V2_MEMBERSHIP_REPORT:
        case IGMP_TYPE_V2_LEAVE_GROUP:
            return 2;
        case IGMP_TYPE_V3_MEMBERSHIP_REPORT:
            return 3;
        case IGMP_TYPE_MEMBERSHIP_QUERY:
            // Query version depends on packet length and content
            return 0; // Will be determined later
        default:
            return 0;
    }
}

static int parse_igmpv3_group_record(nxt_mbuf_t *mbuf, int offset, precord_t *precord, int record_index, ya_fvalue_t* fvArray)
{
    if (nxt_mbuf_get_length(mbuf) < offset + 8) {
        printf("IGMP: insufficient data for IGMPv3 group record\n");
        return -1;
    }

    uint8_t record_type = nxt_mbuf_get_uint8(mbuf, offset);
    uint8_t aux_data_len = nxt_mbuf_get_uint8(mbuf, offset + 1);
    uint16_t num_sources = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 2);
    uint32_t multicast_address = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);

    // Add group record to array as a table
    ya_fvalue_t* fvTable = precord_sub_append(precord, fvArray, table, YA_FT_TABLE);
    precord_sub_put(precord, fvTable, "type", uinteger, YA_FT_UINT8, record_type);
    precord_sub_put(precord, fvTable, "type_name", string, YA_FT_STRING, igmpv3_record_type_name(record_type));
    precord_sub_put(precord, fvTable, "aux_data_len", uinteger, YA_FT_UINT8, aux_data_len);
    precord_sub_put(precord, fvTable, "num_sources", uinteger, YA_FT_UINT16, num_sources);
    precord_sub_put(precord, fvTable, "multicast_address", uinteger, YA_FT_UINT32, multicast_address);

    // Required fields from 开发需求.md (for first record only for backward compatibility)
    if (record_index == 0) {
        precord_put(precord, "recType", uinteger, record_type); // Record type
        precord_put(precord, "recNumSrc", uinteger, num_sources); // Number of sources in record
    }

    printf("IGMP: Group Record %d - Type=%s, Sources=%d, Group=0x%08x\n",
           record_index, igmpv3_record_type_name(record_type), num_sources, multicast_address);

    int record_length = 8 + (num_sources * 4) + (aux_data_len * 4);

    // Parse source addresses and add them to the record table
    if (num_sources > 0) {
        ya_fvalue_t* fvSourceArray = precord_sub_put(precord, fvTable, "sources", array, YA_FT_ARRAY);
        for (int i = 0; i < num_sources && i < 10; i++) { // Limit to 10 sources for performance
            if (nxt_mbuf_get_length(mbuf) < offset + 8 + (i * 4) + 4) {
                break;
            }
            uint32_t source_addr = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 8 + (i * 4));
            ya_fvalue_t* fvSourceTable = precord_sub_append(precord, fvSourceArray, table, YA_FT_TABLE);
            precord_sub_put(precord, fvSourceTable, "address", uinteger, YA_FT_UINT32, source_addr);
        }
    }

    return record_length;
}

static int determine_igmp_version(uint8_t type, int packet_length)
{
    int version = igmp_version_from_type(type);
    if (version != 0) {
        return version;
    }

    // For queries, determine version based on packet length
    if (type == IGMP_TYPE_MEMBERSHIP_QUERY) {
        if (packet_length == 8) {
            return 1; // IGMPv1 query
        } else if (packet_length >= 12) {
            return 3; // IGMPv3 query
        } else {
            return 2; // IGMPv2 query
        }
    }

    return 2; // Default to v2
}

static
int igmp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check minimum IGMP header length (8 bytes)
    if (nxt_mbuf_get_length(mbuf) < 8) {
        printf("IGMP: insufficient data length (%d bytes, need at least 8)\n",
               nxt_mbuf_get_length(mbuf));
        return -1;
    }

    int packet_length = nxt_mbuf_get_length(mbuf);

    // Parse common IGMP header fields
    uint8_t type = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t max_resp_time = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint32_t group_address = nxt_mbuf_get_uint32_ntoh(mbuf, 4);

    // Determine IGMP version
    int version = determine_igmp_version(type, packet_length);

    // Record common IGMP fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "type", uinteger, type);
    precord_put(precord, "type_name", string, igmp_type_name(type));
    precord_put(precord, "max_resp_time", uinteger, max_resp_time);
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "group_address", uinteger, group_address);

    // Required fields from 开发需求.md
    precord_put(precord, "mulAddr", uinteger, group_address); // Multicast address (alias for group_address)

    printf("IGMP: Version=%d, Type=%s (0x%02x), Max Resp Time=%d, Group=0x%08x\n",
           version, igmp_type_name(type), type, max_resp_time, group_address);

    int consumed = 8; // Basic header length

    // Version-specific parsing
    if (type == IGMP_TYPE_V3_MEMBERSHIP_REPORT) {
        // IGMPv3 Membership Report
        if (packet_length < 8) {
            printf("IGMP: IGMPv3 report too short\n");
            return -1;
        }

        // Reserved field (2 bytes) + Number of Group Records (2 bytes)
        uint16_t num_group_records = nxt_mbuf_get_uint16_ntoh(mbuf, 6);
        precord_put(precord, "num_group_records", uinteger, num_group_records);

        // Required fields from 开发需求.md
        precord_put(precord, "recNumMul", uinteger, num_group_records); // Number of multicast records

        printf("IGMP: IGMPv3 Report with %d group records\n", num_group_records);

        // Create group records array
        ya_fvalue_t* fvGroupRecordsArray = precord_put(precord, "group_records", array);

        int offset = 8;
        for (int i = 0; i < num_group_records && i < 20; i++) { // Limit to 20 records
            int record_length = parse_igmpv3_group_record(mbuf, offset, precord, i, fvGroupRecordsArray);
            if (record_length < 0) {
                break;
            }
            offset += record_length;
            if (offset >= packet_length) {
                break;
            }
        }
        consumed = offset;
    } else if (type == IGMP_TYPE_MEMBERSHIP_QUERY && version == 3) {
        // IGMPv3 Query
        if (packet_length >= 12) {
            uint8_t s_flag = (nxt_mbuf_get_uint8(mbuf, 8) >> 3) & 0x1;
            uint8_t qrv = nxt_mbuf_get_uint8(mbuf, 8) & 0x7;
            uint8_t qqic = nxt_mbuf_get_uint8(mbuf, 9);
            uint16_t num_sources = nxt_mbuf_get_uint16_ntoh(mbuf, 10);

            precord_put(precord, "s_flag", uinteger, s_flag);
            precord_put(precord, "qrv", uinteger, qrv);
            precord_put(precord, "qqic", uinteger, qqic);
            precord_put(precord, "num_sources", uinteger, num_sources);

            printf("IGMP: IGMPv3 Query - S=%d, QRV=%d, QQIC=%d, Sources=%d\n",
                   s_flag, qrv, qqic, num_sources);

            consumed = 12 + (num_sources * 4);
        }
    }

    nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);
    return consumed;
}

static
int igmp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "internet group management protocol");

    // Common fields
    pschema_register_field(pschema, "version", YA_FT_UINT8, "IGMP version");
    pschema_register_field_ex(pschema, "type", YA_FT_UINT8, "message type", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "type_name", YA_FT_STRING, "message type name");
    pschema_register_field(pschema, "max_resp_time", YA_FT_UINT8, "maximum response time");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "header checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "group_address", YA_FT_UINT32, "group address", YA_DISPLAY_BASE_HEX);

    // IGMPv3 Query fields
    pschema_register_field(pschema, "s_flag", YA_FT_UINT8, "suppress router-side processing flag");
    pschema_register_field(pschema, "qrv", YA_FT_UINT8, "querier's robustness variable");
    pschema_register_field(pschema, "qqic", YA_FT_UINT8, "querier's query interval code");
    pschema_register_field(pschema, "num_sources", YA_FT_UINT16, "number of sources");

    // IGMPv3 Report fields
    pschema_register_field(pschema, "num_group_records", YA_FT_UINT16, "number of group records");

    // Required fields from 开发需求.md
    pschema_register_field_ex(pschema, "mulAddr", YA_FT_UINT32, "multicast address", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "recType", YA_FT_UINT8, "record type");
    pschema_register_field(pschema, "recNumSrc", YA_FT_UINT16, "number of sources in record");
    pschema_register_field(pschema, "recNumMul", YA_FT_UINT16, "number of multicast records");

    // Group records array
    pschema_register_field(pschema, "group_records", YA_FT_ARRAY, "IGMP group records array");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "igmp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = igmp_schema_reg,
    .dissectFun   = igmp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("ipv4", 2),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(igmp)
{
    nxt_dissector_register(&gDissectorDef);
}
