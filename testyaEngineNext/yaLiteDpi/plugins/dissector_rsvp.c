#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "rsvp"

// RSVP Message Types (RFC 2205)
typedef enum {
    RSVP_MSG_PATH = 1,
    RSVP_MSG_RESV = 2,
    RSVP_MSG_PATHERR = 3,
    RSVP_MSG_RESVERR = 4,
    RSVP_MSG_PATHTEAR = 5,
    RSVP_MSG_RESVTEAR = 6,
    RSVP_MSG_RESVCONF = 7,
    RSVP_MSG_DREQ = 8,
    RSVP_MSG_DREP = 9,
    RSVP_MSG_RESVTEARCONF = 10,
    RSVP_MSG_BUNDLE = 12,
    RSVP_MSG_ACK = 13,
    RSVP_MSG_SREFRESH = 15,
    RSVP_MSG_HELLO = 20,
    <PERSON><PERSON>_MSG_NOTIFY = 21,
    RSVP_MSG_INTEGRITY_CHALLENGE = 25,
    RSVP_MSG_INTEGRITY_RESPONSE = 26,
    RSVP_MSG_RECOVERYPATH = 30
} rsvp_message_type_t;

// RSVP Object Classes (RFC 2205 and extensions)
typedef enum {
    RSVP_CLASS_NULL = 0,
    RSVP_CLASS_SESSION = 1,
    RSVP_CLASS_HOP = 3,
    RSVP_CLASS_RSVP_HOP = 3,  // Alias for HOP
    RSVP_CLASS_INTEGRITY = 4,
    RSVP_CLASS_TIME_VALUES = 5,
    RSVP_CLASS_ERROR = 6,
    RSVP_CLASS_ERROR_SPEC = 6,  // Alias for ERROR
    RSVP_CLASS_SCOPE = 7,
    RSVP_CLASS_STYLE = 8,
    RSVP_CLASS_FLOWSPEC = 9,
    RSVP_CLASS_FILTER_SPEC = 10,
    RSVP_CLASS_SENDER_TEMPLATE = 11,
    RSVP_CLASS_SENDER_TSPEC = 12,
    RSVP_CLASS_ADSPEC = 13,
    RSVP_CLASS_POLICY = 14,
    RSVP_CLASS_POLICY_DATA = 14,  // Alias for POLICY
    RSVP_CLASS_CONFIRM = 15,
    RSVP_CLASS_RESV_CONFIRM = 15,  // Alias for CONFIRM
    RSVP_CLASS_LABEL_REQUEST = 19,
    RSVP_CLASS_LABEL = 20,
    RSVP_CLASS_LABEL_SET = 21,
    RSVP_CLASS_EXPLICIT_ROUTE = 22,
    RSVP_CLASS_RECORD_ROUTE = 23,
    RSVP_CLASS_HELLO = 24,
    RSVP_CLASS_MESSAGE_ID = 25,
    RSVP_CLASS_MESSAGE_ID_ACK = 26,
    RSVP_CLASS_MESSAGE_ID_LIST = 27,
    RSVP_CLASS_RECOVERY_LABEL = 34,
    RSVP_CLASS_UPSTREAM_LABEL = 35,
    RSVP_CLASS_SUGGESTED_LABEL = 36,
    RSVP_CLASS_EXCLUDE_ROUTE = 37,
    RSVP_CLASS_RESTART_CAP = 38,
    RSVP_CLASS_LINK_CAP = 39,
    RSVP_CLASS_CAPABILITY = 40,
    RSVP_CLASS_PROTECTION = 41,
    RSVP_CLASS_FAST_REROUTE = 42,
    RSVP_CLASS_SESSION_ATTRIBUTE = 43,
    RSVP_CLASS_DCLASS = 44,
    RSVP_CLASS_LSP_ATTRIBUTES = 45,
    RSVP_CLASS_LSP_REQUIRED_ATTRIBUTES = 46,
    RSVP_CLASS_ASSOCIATION = 47,
    RSVP_CLASS_LSP_TUNNEL_IF_ID = 48,
    RSVP_CLASS_NOTIFY_REQUEST = 49,
    RSVP_CLASS_ADMIN_STATUS = 50,
    RSVP_CLASS_S2L_SUB_LSP = 51,
    RSVP_CLASS_DETOUR = 52,
    RSVP_CLASS_DIFFSERV = 53,
    RSVP_CLASS_CLASSTYPE = 54,
    RSVP_CLASS_GENERALIZED_UNI = 55,
    RSVP_CLASS_CALL_ID = 56,
    RSVP_CLASS_3GPP2_OBJECT = 57,
    RSVP_CLASS_CALL_ATTRIBUTES = 58,
    RSVP_CLASS_SECONDARY_EXPLICIT_ROUTE = 59,
    RSVP_CLASS_SECONDARY_RECORD_ROUTE = 60,
    RSVP_CLASS_JUNIPER_PROPERTIES = 61,
    // Vendor Private Use classes (224-255)
    RSVP_CLASS_VENDOR_PRIVATE_1 = 224,
    RSVP_CLASS_VENDOR_PRIVATE_2 = 225,
    RSVP_CLASS_VENDOR_PRIVATE_3 = 226,
    RSVP_CLASS_VENDOR_PRIVATE_4 = 227,
    RSVP_CLASS_VENDOR_PRIVATE_5 = 228,
    RSVP_CLASS_VENDOR_PRIVATE_6 = 229,
    RSVP_CLASS_VENDOR_PRIVATE_7 = 230,
    RSVP_CLASS_VENDOR_PRIVATE_8 = 231,
    RSVP_CLASS_VENDOR_PRIVATE_9 = 232,
    RSVP_CLASS_VENDOR_PRIVATE_10 = 233,
    RSVP_CLASS_VENDOR_PRIVATE_11 = 234,
    RSVP_CLASS_VENDOR_PRIVATE_12 = 235
} rsvp_class_t;

static const char* rsvp_msg_type_name(uint8_t msg_type)
{
    switch (msg_type) {
        case RSVP_MSG_PATH:           return "Path";
        case RSVP_MSG_RESV:           return "Resv";
        case RSVP_MSG_PATHERR:        return "PathErr";
        case RSVP_MSG_RESVERR:        return "ResvErr";
        case RSVP_MSG_PATHTEAR:       return "PathTear";
        case RSVP_MSG_RESVTEAR:       return "ResvTear";
        case RSVP_MSG_RESVCONF:       return "ResvConf";
        case RSVP_MSG_DREQ:           return "DREQ";
        case RSVP_MSG_DREP:           return "DREP";
        case RSVP_MSG_RESVTEARCONF:   return "ResvTearConfirm";
        case RSVP_MSG_BUNDLE:         return "Bundle";
        case RSVP_MSG_ACK:            return "ACK";
        case RSVP_MSG_SREFRESH:       return "Srefresh";
        case RSVP_MSG_HELLO:          return "Hello";
        case RSVP_MSG_NOTIFY:         return "Notify";
        case RSVP_MSG_INTEGRITY_CHALLENGE: return "Integrity Challenge";
        case RSVP_MSG_INTEGRITY_RESPONSE:  return "Integrity Response";
        case RSVP_MSG_RECOVERYPATH:   return "RecoveryPath";
        default:                      return "Unknown";
    }
}

static const char* rsvp_object_class_name(uint8_t class_num)
{
    switch (class_num) {
        case RSVP_CLASS_NULL:                     return "NULL";
        case RSVP_CLASS_SESSION:                  return "SESSION";
        case RSVP_CLASS_HOP:                      return "HOP";
        case RSVP_CLASS_INTEGRITY:                return "INTEGRITY";
        case RSVP_CLASS_TIME_VALUES:              return "TIME_VALUES";
        case RSVP_CLASS_ERROR:                    return "ERROR";
        case RSVP_CLASS_SCOPE:                    return "SCOPE";
        case RSVP_CLASS_STYLE:                    return "STYLE";
        case RSVP_CLASS_FLOWSPEC:                 return "FLOWSPEC";
        case RSVP_CLASS_FILTER_SPEC:              return "FILTER_SPEC";
        case RSVP_CLASS_SENDER_TEMPLATE:          return "SENDER_TEMPLATE";
        case RSVP_CLASS_SENDER_TSPEC:             return "SENDER_TSPEC";
        case RSVP_CLASS_ADSPEC:                   return "ADSPEC";
        case RSVP_CLASS_POLICY:                   return "POLICY";
        case RSVP_CLASS_CONFIRM:                  return "CONFIRM";
        case RSVP_CLASS_LABEL_REQUEST:            return "LABEL_REQUEST";
        case RSVP_CLASS_LABEL:                    return "LABEL";
        case RSVP_CLASS_LABEL_SET:                return "LABEL_SET";
        case RSVP_CLASS_EXPLICIT_ROUTE:           return "EXPLICIT_ROUTE";
        case RSVP_CLASS_RECORD_ROUTE:             return "RECORD_ROUTE";
        case RSVP_CLASS_HELLO:                    return "HELLO";
        case RSVP_CLASS_MESSAGE_ID:               return "MESSAGE_ID";
        case RSVP_CLASS_MESSAGE_ID_ACK:           return "MESSAGE_ID_ACK";
        case RSVP_CLASS_MESSAGE_ID_LIST:          return "MESSAGE_ID_LIST";
        case RSVP_CLASS_RECOVERY_LABEL:           return "RECOVERY_LABEL";
        case RSVP_CLASS_UPSTREAM_LABEL:           return "UPSTREAM_LABEL";
        case RSVP_CLASS_SUGGESTED_LABEL:          return "SUGGESTED_LABEL";
        case RSVP_CLASS_EXCLUDE_ROUTE:            return "EXCLUDE_ROUTE";
        case RSVP_CLASS_RESTART_CAP:              return "RESTART_CAP";
        case RSVP_CLASS_LINK_CAP:                 return "LINK_CAP";
        case RSVP_CLASS_CAPABILITY:               return "CAPABILITY";
        case RSVP_CLASS_PROTECTION:               return "PROTECTION";
        case RSVP_CLASS_FAST_REROUTE:             return "FAST_REROUTE";
        case RSVP_CLASS_SESSION_ATTRIBUTE:        return "SESSION_ATTRIBUTE";
        case RSVP_CLASS_DCLASS:                   return "DCLASS";
        case RSVP_CLASS_LSP_ATTRIBUTES:           return "LSP_ATTRIBUTES";
        case RSVP_CLASS_LSP_REQUIRED_ATTRIBUTES:  return "LSP_REQUIRED_ATTRIBUTES";
        case RSVP_CLASS_ASSOCIATION:              return "ASSOCIATION";
        case RSVP_CLASS_LSP_TUNNEL_IF_ID:         return "LSP_TUNNEL_IF_ID";
        case RSVP_CLASS_NOTIFY_REQUEST:           return "NOTIFY_REQUEST";
        case RSVP_CLASS_ADMIN_STATUS:             return "ADMIN_STATUS";
        case RSVP_CLASS_S2L_SUB_LSP:              return "S2L_SUB_LSP";
        case RSVP_CLASS_DETOUR:                   return "DETOUR";
        case RSVP_CLASS_DIFFSERV:                 return "DIFFSERV";
        case RSVP_CLASS_CLASSTYPE:                return "CLASSTYPE";
        case RSVP_CLASS_GENERALIZED_UNI:          return "GENERALIZED_UNI";
        case RSVP_CLASS_CALL_ID:                  return "CALL_ID";
        case RSVP_CLASS_3GPP2_OBJECT:             return "3GPP2_OBJECT";
        case RSVP_CLASS_CALL_ATTRIBUTES:          return "CALL_ATTRIBUTES";
        case RSVP_CLASS_SECONDARY_EXPLICIT_ROUTE: return "SECONDARY_EXPLICIT_ROUTE";
        case RSVP_CLASS_SECONDARY_RECORD_ROUTE:   return "SECONDARY_RECORD_ROUTE";
        case RSVP_CLASS_JUNIPER_PROPERTIES:       return "JUNIPER_PROPERTIES";
        case RSVP_CLASS_VENDOR_PRIVATE_1:         return "VENDOR_PRIVATE_1";
        case RSVP_CLASS_VENDOR_PRIVATE_2:         return "VENDOR_PRIVATE_2";
        case RSVP_CLASS_VENDOR_PRIVATE_3:         return "VENDOR_PRIVATE_3";
        case RSVP_CLASS_VENDOR_PRIVATE_4:         return "VENDOR_PRIVATE_4";
        case RSVP_CLASS_VENDOR_PRIVATE_5:         return "VENDOR_PRIVATE_5";
        case RSVP_CLASS_VENDOR_PRIVATE_6:         return "VENDOR_PRIVATE_6";
        case RSVP_CLASS_VENDOR_PRIVATE_7:         return "VENDOR_PRIVATE_7";
        case RSVP_CLASS_VENDOR_PRIVATE_8:         return "VENDOR_PRIVATE_8";
        case RSVP_CLASS_VENDOR_PRIVATE_9:         return "VENDOR_PRIVATE_9";
        case RSVP_CLASS_VENDOR_PRIVATE_10:        return "VENDOR_PRIVATE_10";
        case RSVP_CLASS_VENDOR_PRIVATE_11:        return "VENDOR_PRIVATE_11";
        case RSVP_CLASS_VENDOR_PRIVATE_12:        return "VENDOR_PRIVATE_12";
        default:                                  return "Unknown";
    }
}

// RSVP header structure
typedef struct {
    uint8_t version_flags;  // Version (4 bits) + Flags (4 bits)
    uint8_t msg_type;       // Message Type
    uint16_t checksum;      // Checksum
    uint8_t ttl;            // TTL
    uint8_t reserved;       // Reserved
    uint16_t length;        // Message Length
} rsvp_header_t;

// RSVP object header structure
typedef struct {
    uint16_t length;        // Object Length
    uint8_t class_num;      // Object Class Number
    uint8_t c_type;         // Object C-Type
} rsvp_object_header_t;

// SESSION object for IPv4 (C-Type 1)
typedef struct {
    uint32_t dest_addr;     // Destination Address
    uint8_t protocol_id;    // Protocol ID
    uint8_t flags;          // Flags
    uint16_t dest_port;     // Destination Port
} rsvp_session_ipv4_t;

// RSVP_HOP object for IPv4 (C-Type 1)
typedef struct {
    uint32_t next_hop_addr; // Next/Previous Hop Address
    uint32_t logical_if;    // Logical Interface Handle
} rsvp_hop_ipv4_t;

// TIME_VALUES object (C-Type 1)
typedef struct {
    uint32_t refresh_period; // Refresh Period (ms)
} rsvp_time_values_t;

// ERROR_SPEC object for IPv4 (C-Type 1)
typedef struct {
    uint32_t error_node_addr; // Error Node Address
    uint8_t flags;            // Flags
    uint8_t error_code;       // Error Code
    uint16_t error_value;     // Error Value
} rsvp_error_spec_ipv4_t;

// RSVP object parsing function
static void rsvp_parse_object(nxt_mbuf_t *mbuf, uint32_t offset, uint16_t obj_length, uint8_t obj_class, uint8_t obj_ctype)
{
    // Parse specific object types and print detailed information
    if (obj_length >= 8) { // Minimum for most objects
        switch (obj_class) {
            case RSVP_CLASS_SESSION:
                if (obj_ctype == 1 && obj_length >= 12) { // IPv4 SESSION
                    uint32_t dest_addr = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                    uint8_t protocol_id = nxt_mbuf_get_uint8(mbuf, offset + 8);
                    uint8_t session_flags = nxt_mbuf_get_uint8(mbuf, offset + 9);
                    uint16_t dest_port = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 10);

                    printf("  SESSION: Dest=%d.%d.%d.%d, Protocol=%d, Flags=0x%02x, Port=%d\n",
                           (dest_addr >> 24) & 0xFF, (dest_addr >> 16) & 0xFF,
                           (dest_addr >> 8) & 0xFF, dest_addr & 0xFF,
                           protocol_id, session_flags, dest_port);
                }
                break;

            case RSVP_CLASS_HOP:
                if (obj_ctype == 1 && obj_length >= 12) { // IPv4 HOP
                    uint32_t next_hop = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                    uint32_t logical_if = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 8);

                    printf("  HOP: NextHop=%d.%d.%d.%d, LogicalIF=0x%08x\n",
                           (next_hop >> 24) & 0xFF, (next_hop >> 16) & 0xFF,
                           (next_hop >> 8) & 0xFF, next_hop & 0xFF, logical_if);
                }
                break;

            case RSVP_CLASS_TIME_VALUES:
                if (obj_ctype == 1 && obj_length >= 8) { // TIME_VALUES
                    uint32_t refresh_period = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);

                    printf("  TIME_VALUES: RefreshPeriod=%d ms\n", refresh_period);
                }
                break;

            case RSVP_CLASS_ERROR:
                if (obj_ctype == 1 && obj_length >= 12) { // IPv4 ERROR
                    uint32_t error_node = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                    uint8_t error_flags = nxt_mbuf_get_uint8(mbuf, offset + 8);
                    uint8_t error_code = nxt_mbuf_get_uint8(mbuf, offset + 9);
                    uint16_t error_value = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 10);

                    printf("  ERROR: ErrorNode=%d.%d.%d.%d, Flags=0x%02x, Code=%d, Value=%d\n",
                           (error_node >> 24) & 0xFF, (error_node >> 16) & 0xFF,
                           (error_node >> 8) & 0xFF, error_node & 0xFF,
                           error_flags, error_code, error_value);
                }
                break;

            case RSVP_CLASS_STYLE:
                if (obj_ctype == 1 && obj_length >= 8) { // STYLE
                    uint32_t style_value = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
                    printf("  STYLE: Value=0x%08x\n", style_value);
                }
                break;

            case RSVP_CLASS_FLOWSPEC:
                printf("  FLOWSPEC: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_FILTER_SPEC:
                printf("  FILTER_SPEC: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_SENDER_TEMPLATE:
                printf("  SENDER_TEMPLATE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_SENDER_TSPEC:
                printf("  SENDER_TSPEC: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_SCOPE:
                printf("  SCOPE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_CONFIRM:
                printf("  CONFIRM: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_LABEL_REQUEST:
                printf("  LABEL_REQUEST: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_RECOVERY_LABEL:
            case RSVP_CLASS_UPSTREAM_LABEL:
            case RSVP_CLASS_SUGGESTED_LABEL:
            case RSVP_CLASS_LABEL:
                printf("  LABEL: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_LABEL_SET:
                printf("  LABEL_SET: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_SESSION_ATTRIBUTE:
                printf("  SESSION_ATTRIBUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_EXPLICIT_ROUTE:
                printf("  EXPLICIT_ROUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_RECORD_ROUTE:
                printf("  RECORD_ROUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_EXCLUDE_ROUTE:
                printf("  EXCLUDE_ROUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_MESSAGE_ID:
                printf("  MESSAGE_ID: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_MESSAGE_ID_ACK:
                printf("  MESSAGE_ID_ACK: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_MESSAGE_ID_LIST:
                printf("  MESSAGE_ID_LIST: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_HELLO:
                printf("  HELLO: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_DCLASS:
                printf("  DCLASS: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_ADMIN_STATUS:
                printf("  ADMIN_STATUS: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_LSP_ATTRIBUTES:
            case RSVP_CLASS_LSP_REQUIRED_ATTRIBUTES:
                printf("  LSP_ATTRIBUTES: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_ASSOCIATION:
                printf("  ASSOCIATION: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_LSP_TUNNEL_IF_ID:
                printf("  LSP_TUNNEL_IF_ID: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_NOTIFY_REQUEST:
                printf("  NOTIFY_REQUEST: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_GENERALIZED_UNI:
                printf("  GENERALIZED_UNI: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_CALL_ID:
                printf("  CALL_ID: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_3GPP2_OBJECT:
                printf("  3GPP2_OBJECT: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_RESTART_CAP:
                printf("  RESTART_CAP: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_LINK_CAP:
                printf("  LINK_CAP: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_CAPABILITY:
                printf("  CAPABILITY: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_PROTECTION:
                printf("  PROTECTION: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_FAST_REROUTE:
                printf("  FAST_REROUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_S2L_SUB_LSP:
                printf("  S2L_SUB_LSP: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_DETOUR:
                printf("  DETOUR: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_DIFFSERV:
                printf("  DIFFSERV: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_CLASSTYPE:
                printf("  CLASSTYPE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_VENDOR_PRIVATE_1:
            case RSVP_CLASS_VENDOR_PRIVATE_2:
            case RSVP_CLASS_VENDOR_PRIVATE_3:
            case RSVP_CLASS_VENDOR_PRIVATE_4:
            case RSVP_CLASS_VENDOR_PRIVATE_5:
            case RSVP_CLASS_VENDOR_PRIVATE_6:
            case RSVP_CLASS_VENDOR_PRIVATE_7:
            case RSVP_CLASS_VENDOR_PRIVATE_8:
            case RSVP_CLASS_VENDOR_PRIVATE_9:
            case RSVP_CLASS_VENDOR_PRIVATE_10:
            case RSVP_CLASS_VENDOR_PRIVATE_11:
            case RSVP_CLASS_VENDOR_PRIVATE_12:
                printf("  VENDOR_PRIVATE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_SECONDARY_EXPLICIT_ROUTE:
                printf("  SECONDARY_EXPLICIT_ROUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_SECONDARY_RECORD_ROUTE:
                printf("  SECONDARY_RECORD_ROUTE: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_CALL_ATTRIBUTES:
                printf("  CALL_ATTRIBUTES: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_JUNIPER_PROPERTIES:
                printf("  JUNIPER_PROPERTIES: Length=%d bytes (detailed parsing not implemented)\n", obj_length - 4);
                break;

            case RSVP_CLASS_NULL:
            default:
                printf("  Unknown/Unhandled Object: Class=%d, Length=%d bytes\n", obj_class, obj_length - 4);
                break;
        }
    }
}

static
int rsvp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
  
  // Check minimum RSVP header length (8 bytes)
  if (nxt_mbuf_get_length(mbuf) < 8) {
    printf("RSVP: insufficient data length (%d bytes, need at least 8)\n", 
      nxt_mbuf_get_length(mbuf));
      return -1;
    }
    
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);
    // Parse RSVP common header
    uint8_t version_flags = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t msg_type = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t checksum = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    uint8_t ttl = nxt_mbuf_get_uint8(mbuf, 4);
    uint8_t reserved _U_ = nxt_mbuf_get_uint8(mbuf, 5);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 6);

    uint8_t version = (version_flags >> 4) & 0x0F;
    uint8_t flags = version_flags & 0x0F;

    // Record RSVP fields
    precord_put(precord, "version", uinteger, version);
    precord_put(precord, "flags", uinteger, flags);
    precord_put(precord, "msg_type", uinteger, msg_type);
    precord_put(precord, "msg_type_name", string, rsvp_msg_type_name(msg_type));
    precord_put(precord, "checksum", uinteger, checksum);
    precord_put(precord, "ttl", uinteger, ttl);
    precord_put(precord, "length", uinteger, length);

    // Validate message length
    if (length < 8) {
        printf("RSVP: invalid message length %d (minimum 8)\n", length);
        return -1;
    }

    if (nxt_mbuf_get_length(mbuf) < length) {
        printf("RSVP: truncated message (have %d bytes, need %d)\n",
               nxt_mbuf_get_length(mbuf), length);
        return -1;
    }

    // Parse RSVP objects
    uint32_t offset = 8; // Start after common header
    uint32_t object_count = 0;

    while (offset + 4 <= length) {
        // Parse object header
        uint16_t obj_length = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
        uint8_t obj_class = nxt_mbuf_get_uint8(mbuf, offset + 2);
        uint8_t obj_ctype = nxt_mbuf_get_uint8(mbuf, offset + 3);

        if (obj_length < 4) {
            printf("RSVP: invalid object length %d (minimum 4)\n", obj_length);
            break;
        }

        if (offset + obj_length > length) {
            printf("RSVP: object extends beyond message (offset %d + length %d > %d)\n",
                   offset, obj_length, length);
            break;
        }

        // Record object information - use printf for detailed object info
        printf("RSVP Object %d: Class=%d (%s), C-Type=%d, Length=%d\n",
               object_count, obj_class, rsvp_object_class_name(obj_class), obj_ctype, obj_length);

        // Parse specific object types using dedicated function
        rsvp_parse_object(mbuf, offset, obj_length, obj_class, obj_ctype);

        // Move to next object
        offset += obj_length;
        object_count++;

        // Prevent infinite loop with too many objects
        if (object_count >= 50) {
            printf("RSVP: too many objects (>= 50), stopping parsing\n");
            break;
        }
    }

    precord_put(precord, "object_count", uinteger, object_count);

    printf("RSVP: Version=%d, Type=%s, TTL=%d, Length=%d, Objects=%d\n",
           version, rsvp_msg_type_name(msg_type), ttl, length, object_count);
    // nxt_session_post_event(engine, session, NXT_EVENT_SESSION_MESSAGE, mbuf, precord);

    return length; // Return full message length
}

static
int rsvp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "resource reservation protocol");
    pschema_register_field(pschema, "version", YA_FT_UINT8, "protocol version");
    pschema_register_field_ex(pschema, "flags", YA_FT_UINT8, "header flags", YA_DISPLAY_BASE_HEX);
    pschema_register_field_ex(pschema, "msg_type", YA_FT_UINT8, "message type", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "msg_type_name", YA_FT_STRING, "message type name");
    pschema_register_field_ex(pschema, "checksum", YA_FT_UINT16, "header checksum", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "ttl", YA_FT_UINT8, "time to live");
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    pschema_register_field(pschema, "object_count", YA_FT_UINT32, "number of objects");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "rsvp",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = rsvp_schema_reg,
    .dissectFun   = rsvp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("ipv4", 46),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(rsvp)
{
    nxt_dissector_register(&gDissectorDef);
}
