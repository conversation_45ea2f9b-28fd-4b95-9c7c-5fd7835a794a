#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <stdbool.h>

#define PROTO_NAME "pptp"

// PPTP Protocol Types
typedef enum {
    PPTP_PROTOCOL_TYPE = 0x880B  // PPTP protocol type in GRE
} pptp_protocol_t;

// PPTP Message Types (RFC 2637)
typedef enum {
    PPTP_MSG_TYPE_CONTROL = 1,
    PPTP_MSG_TYPE_MANAGEMENT = 2
} pptp_message_type_t;

// PPTP Control Message Types
typedef enum {
    PPTP_START_SESSION_REQUEST = 1,
    PPTP_START_SESSION_REPLY = 2,
    PPTP_STOP_SESSION_REQUEST = 3,
    PPTP_STOP_SESSION_REPLY = 4,
    PPTP_ECHO_REQUEST = 5,
    PPTP_ECHO_REPLY = 6,
    PPTP_OUT_CALL_REQUEST = 7,
    PPTP_OUT_CALL_REPLY = 8,
    PPTP_IN_CALL_REQUEST = 9,
    PPTP_IN_CALL_REPLY = 10,
    PPTP_IN_CALL_CONNECTED = 11,
    PPTP_CALL_CLEAR_REQUEST = 12,
    PPTP_CALL_DISCONNECT_NOTIFY = 13,
    PPTP_WAN_ERROR_NOTIFY = 14,
    PPTP_SET_LINK_INFO = 15
} pptp_control_type_t;

static const char* pptp_message_type_name(uint16_t msg_type)
{
    switch (msg_type) {
        case PPTP_MSG_TYPE_CONTROL:    return "Control Message";
        case PPTP_MSG_TYPE_MANAGEMENT: return "Management Message";
        default:                       return "Unknown";
    }
}

static const char* pptp_control_type_name(uint16_t ctrl_type)
{
    switch (ctrl_type) {
        case PPTP_START_SESSION_REQUEST:   return "Start-Session-Request";
        case PPTP_START_SESSION_REPLY:     return "Start-Session-Reply";
        case PPTP_STOP_SESSION_REQUEST:    return "Stop-Session-Request";
        case PPTP_STOP_SESSION_REPLY:      return "Stop-Session-Reply";
        case PPTP_ECHO_REQUEST:            return "Echo-Request";
        case PPTP_ECHO_REPLY:              return "Echo-Reply";
        case PPTP_OUT_CALL_REQUEST:        return "Outgoing-Call-Request";
        case PPTP_OUT_CALL_REPLY:          return "Outgoing-Call-Reply";
        case PPTP_IN_CALL_REQUEST:         return "Incoming-Call-Request";
        case PPTP_IN_CALL_REPLY:           return "Incoming-Call-Reply";
        case PPTP_IN_CALL_CONNECTED:       return "Incoming-Call-Connected";
        case PPTP_CALL_CLEAR_REQUEST:      return "Call-Clear-Request";
        case PPTP_CALL_DISCONNECT_NOTIFY:  return "Call-Disconnect-Notify";
        case PPTP_WAN_ERROR_NOTIFY:        return "WAN-Error-Notify";
        case PPTP_SET_LINK_INFO:           return "Set-Link-Info";
        default:                           return "Unknown";
    }
}

// Helper function to parse PPTP control header
static int parse_pptp_control_header(nxt_mbuf_t *mbuf, int offset, precord_t *precord)
{
    if (nxt_mbuf_get_length(mbuf) < offset + 12) {
        printf("PPTP: insufficient data for control header\n");
        return -1;
    }

    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, offset);
    uint16_t msg_type = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 2);
    uint32_t magic_cookie = nxt_mbuf_get_uint32_ntoh(mbuf, offset + 4);
    uint16_t control_type = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 8);
    uint16_t reserved = nxt_mbuf_get_uint16_ntoh(mbuf, offset + 10);
    (void)reserved; // Suppress unused variable warning

    precord_put(precord, "length", uinteger, length);
    precord_put(precord, "message_type", uinteger, msg_type);
    precord_put(precord, "message_type_name", string, pptp_message_type_name(msg_type));
    precord_put(precord, "magic_cookie", uinteger, magic_cookie);
    precord_put(precord, "control_type", uinteger, control_type);
    precord_put(precord, "control_type_name", string, pptp_control_type_name(control_type));

    printf("PPTP Control: Length=%d, Type=%s, Magic=0x%08x, Control=%s\n",
           length, pptp_message_type_name(msg_type), magic_cookie, 
           pptp_control_type_name(control_type));

    return 12; // Control header is 12 bytes
}

static int pptp_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    int packet_length = nxt_mbuf_get_length(mbuf);
    if (packet_length < 2) {
        printf("PPTP: insufficient data length (%d bytes)\n", packet_length);
        return -1;
    }

    // Determine if this is a control message or data packet
    // Control messages start with length field (typically > 12)
    // Data packets are PPP frames (start with 0xFF 0x03 or protocol field)
    
    uint16_t first_word = nxt_mbuf_get_uint16_ntoh(mbuf, 0);

    if (first_word >= 12 && first_word <= 1500) {
      // Likely a control message (reasonable length)
      printf("PPTP: Processing as control message\n");
      return parse_pptp_control_header(mbuf, 0, precord);
    } else {
      return -1;
    }
}

static int pptp_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "point to point tunneling protocol");

    // PPTP Control Message fields
    pschema_register_field(pschema, "length", YA_FT_UINT16, "message length");
    pschema_register_field(pschema, "message_type", YA_FT_UINT16, "message type");
    pschema_register_field(pschema, "message_type_name", YA_FT_STRING, "message type name");
    pschema_register_field_ex(pschema, "magic_cookie", YA_FT_UINT32, "magic cookie", YA_DISPLAY_BASE_HEX);
    pschema_register_field(pschema, "control_type", YA_FT_UINT16, "control message type");
    pschema_register_field(pschema, "control_type_name", YA_FT_STRING, "control message type name");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "pptp",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = pptp_schema_reg,
    .dissectFun   = pptp_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("tcp", 1723),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(pptp)
{
    nxt_dissector_register(&gDissectorDef);


}
